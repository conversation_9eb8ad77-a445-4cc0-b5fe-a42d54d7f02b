<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HLS 播放器</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: #000;
        margin: 0;
        padding: 0;
        overflow: auto;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    </style>
  </head>
  <body>
    <div class="video-wrapper">
      <video id="video" class="video-player" controls muted autoplay></video>
    </div>

    <!-- 引入 HLS.js 库 -->
    <script src="hls.min.js"></script>

    <script>
      // 播放器实例数组
      const players = null;

      // 初始化单个播放器
      function initPlayer(index) {
        const video = document.getElementById(`video`);
        const hlsUrl = "http://*************:8999/live/test_sun/stream1/index.m3u8";

        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
          });

          hls.loadSource(hlsUrl);
          hls.attachMedia(video);

          // 监听清单解析成功事件
          hls.on(Hls.Events.MANIFEST_PARSED, function () {
            console.log(`播放器: 清单解析成功，开始播放`);
            video.play().catch((e) => {
              console.error(`播放器: 播放失败`, e);
            });
          });

          // 监听错误事件
          hls.on(Hls.Events.ERROR, function (event, data) {
            console.error(`播放器: HLS错误`, data);
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  console.log(`播放器: 网络错误，尝试下一个URL`);
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  console.log(`播放器: 媒体错误，尝试恢复`);
                  hls.recoverMediaError();
                  break;
                default:
                  console.log(`播放器: 致命错误，尝试下一个URL`);
                  break;
              }
            }
          });
        } else {
          console.error(`播放器: 浏览器不支持HLS播放`);
        }
      }

      // 初始化所有播放器
      function initAllPlayers() {
        initPlayer();
      }

      // 页面加载完成后初始化所有播放器
      document.addEventListener("DOMContentLoaded", initAllPlayers);

      // 页面卸载时清理资源
      window.addEventListener("beforeunload", function () {
        if (players && players.destroy) {
          console.log(`清理播放器`);
          players.destroy();
        }
      });
    </script>
  </body>
</html>
