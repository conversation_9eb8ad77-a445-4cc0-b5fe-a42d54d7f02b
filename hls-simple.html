<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HLS 播放器</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: #000;
        margin: 0;
        padding: 0;
        overflow: auto;
      }

      .video-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2px;
        padding: 2px;
        min-height: 100vh;
      }

      .video-container {
        position: relative;
        background: #000;
        border: 1px solid #333;
      }

      .video-wrapper {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background: #8b2727;
      }

      .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* 平板端：2列3行布局 */
      @media (max-width: 1024px) {
        .video-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      /* 移动端：1列6行布局 */
      @media (max-width: 768px) {
        .video-grid {
          grid-template-columns: 1fr;
          gap: 1px;
          padding: 1px;
        }

        .video-wrapper {
          padding-bottom: 56.25%;
        }
      }
    </style>
  </head>
  <body>
    <div class="video-grid">
      <div class="video-wrapper">
        <video
          id="video0"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://192.168.70.43:8999/live/test_sun/stream1/"
        ></video>
      </div>

      <div class="video-wrapper">
        <video
          id="video1"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://*************:8889/live/pro/l6uyK7X4Bh/stream2"
        ></video>
      </div>

      <div class="video-wrapper">
        <video
          id="video2"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://*************:8889/live/pro/l6uyK7X4Bh/stream1"
        ></video>
      </div>

      <div class="video-wrapper">
        <video
          id="video3"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://*************:8889/live/pro/l6uyK7X4Bh/stream4"
        ></video>
      </div>

      <div class="video-wrapper">
        <video
          id="video4"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://*************:8889/live/pro/l6uyK7X4Bh/stream3"
        ></video>
      </div>

      <div class="video-wrapper">
        <video
          id="video5"
          class="video-player"
          controls
          muted
          autoplay
          data-hls-url="http://*************:8889/live/pro/l6uyK7X4Bh/stream5"
        ></video>
      </div>
    </div>

    <!-- 引入 HLS.js 库 -->
    <script src="hls.min.js"></script>

    <script>
      // 播放器实例数组
      const players = [];
      const playerCount = 6;

      // 生成可能的URL变体
      function generateUrlVariants(baseUrl) {
        const variants = [];
        let url = baseUrl.trim();

        // 移除末尾的斜杠
        if (url.endsWith("/")) {
          url = url.slice(0, -1);
        }

        // 原始URL
        variants.push(baseUrl.trim());

        // 添加常见的流媒体文件扩展名
        const extensions = [".m3u8", "/playlist.m3u8", "/index.m3u8"];
        extensions.forEach((ext) => {
          variants.push(url + ext);
        });

        return variants;
      }

      // 初始化单个播放器
      function initPlayer(index) {
        const video = document.getElementById(`video${index}`);

        // 从video标签的data-hls-url属性获取URL地址
        const hlsUrl = video.getAttribute("data-hls-url");

        if (!hlsUrl) {
          console.error(`播放器${index + 1}: 未找到HLS URL地址`);
          return;
        }

        console.log(`播放器${index + 1}: 从标签获取URL: ${hlsUrl}`);

        if (Hls.isSupported()) {
          // 使用 HLS.js
          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
          });

          // 保存播放器实例
          players[index] = hls;

          // 尝试不同的URL格式
          const urlVariants = generateUrlVariants(hlsUrl);
          let currentIndex = 0;

          function tryNextUrl() {
            if (currentIndex >= urlVariants.length) {
              console.error(`播放器${index + 1}: 所有URL格式都尝试失败`);
              return;
            }

            const currentUrl = urlVariants[currentIndex];
            console.log(`播放器${index + 1}: 尝试加载 ${currentUrl}`);

            hls.loadSource(currentUrl);
            hls.attachMedia(video);

            // 监听清单解析成功事件
            hls.on(Hls.Events.MANIFEST_PARSED, function () {
              console.log(`播放器${index + 1}: 清单解析成功，开始播放`);
              video.play().catch((e) => {
                console.error(`播放器${index + 1}: 播放失败`, e);
              });
            });

            // 监听错误事件
            hls.on(Hls.Events.ERROR, function (event, data) {
              console.error(`播放器${index + 1}: HLS错误`, data);
              if (data.fatal) {
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    console.log(`播放器${index + 1}: 网络错误，尝试下一个URL`);
                    currentIndex++;
                    setTimeout(tryNextUrl, 2000);
                    break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    console.log(`播放器${index + 1}: 媒体错误，尝试恢复`);
                    hls.recoverMediaError();
                    break;
                  default:
                    console.log(`播放器${index + 1}: 致命错误，尝试下一个URL`);
                    currentIndex++;
                    setTimeout(tryNextUrl, 2000);
                    break;
                }
              }
            });
          }

          // 开始尝试第一个URL
          tryNextUrl();
        } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
          // 原生支持 HLS (Safari)
          console.log(`播放器${index + 1}: 使用原生HLS支持`);
          const urlVariants = generateUrlVariants(hlsUrl);

          function tryNativeHls(urlIndex = 0) {
            if (urlIndex >= urlVariants.length) {
              console.error(`播放器${index + 1}: 所有URL格式都尝试失败`);
              return;
            }

            const currentUrl = urlVariants[urlIndex];
            console.log(`播放器${index + 1}: 尝试原生播放 ${currentUrl}`);

            video.src = currentUrl;

            const onLoadedMetadata = function () {
              console.log(`播放器${index + 1}: 原生HLS加载成功`);
              video.play().catch((e) => {
                console.error(`播放器${index + 1}: 播放失败`, e);
              });
              video.removeEventListener("loadedmetadata", onLoadedMetadata);
              video.removeEventListener("error", onError);
            };

            const onError = function () {
              console.log(`播放器${index + 1}: 原生HLS加载失败，尝试下一个URL`);
              video.removeEventListener("loadedmetadata", onLoadedMetadata);
              video.removeEventListener("error", onError);
              tryNativeHls(urlIndex + 1);
            };

            video.addEventListener("loadedmetadata", onLoadedMetadata);
            video.addEventListener("error", onError);
          }

          tryNativeHls();
        } else {
          console.error(`播放器${index + 1}: 浏览器不支持HLS播放`);
        }
      }

      // 初始化所有播放器
      function initAllPlayers() {
        for (let i = 0; i < playerCount; i++) {
          // 延迟初始化每个播放器，避免同时请求过多资源
          setTimeout(() => {
            initPlayer(i);
          }, i * 500); // 每个播放器延迟500ms初始化
        }
      }

      // 页面加载完成后初始化所有播放器
      document.addEventListener("DOMContentLoaded", initAllPlayers);

      // 页面卸载时清理资源
      window.addEventListener("beforeunload", function () {
        players.forEach((hls, index) => {
          if (hls && hls.destroy) {
            console.log(`清理播放器${index + 1}`);
            hls.destroy();
          }
        });
      });
    </script>
  </body>
</html>