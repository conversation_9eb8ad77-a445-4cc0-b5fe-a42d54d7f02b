<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>原生浏览器播放</title>
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    background: #000;
    margin: 0;
    padding: 0;
    overflow: auto;
  }
  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
</style>
</head>
<body>
<div class="video-wrapper">
  <video id="video" class="video-player" controls muted autoplay></video>
</div>

<script>
  const video = document.getElementById('video');
  const videoSrc = "http://*************:8999/live/test_sun/stream1/index.m3u8";
  video.src = videoSrc;

  video.play().catch((e) => {
    console.error('播放失败:', e);
  });
</script>
</body>
</html>
